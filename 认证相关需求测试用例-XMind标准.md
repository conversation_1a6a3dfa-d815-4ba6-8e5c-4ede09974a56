# 【认证相关需求】-测试用例

## 需求1：【认证】核身接口增加传参控制是否展示协议

### 功能测试

#### TL-个人核身网页接口showAgreement为true时协议完整展示验证

##### PD-前置条件：核身接口可用；协议内容已配置；

##### 步骤一：调用个人核身网页接口/identity_service/sg2nty；步骤二：请求参数中设置showAgreement=true；步骤三：提交核身请求；步骤四：检查返回的核身页面内容；步骤五：验证页面中协议展示情况；步骤六：检查短信中协议链接；

##### ER-预期结果：1：接口调用成功返回200状态码；2：核身页面正常渲染；3：页面显示数字证书协议、服务协议和隐私政策；4：协议链接可点击访问；5：短信验证码中包含协议链接；6：协议展示优先级高于签署侧校验；

#### TL-个人核身网页接口showAgreement为false时协议隐藏验证

##### PD-前置条件：核身接口可用；

##### 步骤一：调用个人核身网页接口/identity_service/sg2nty；步骤二：请求参数中设置showAgreement=false；步骤三：提交核身请求；步骤四：检查返回的核身页面内容；步骤五：验证页面中协议展示情况；步骤六：检查短信内容；

##### ER-预期结果：1：接口调用成功返回200状态码；2：核身页面正常渲染；3：页面中不显示任何协议内容；4：短信验证码中不包含协议链接；5：核身功能正常可用；

#### TL-个人核身扫脸api接口showAgreement参数功能验证

##### PD-前置条件：扫脸接口可用；摄像头权限已授权；

##### 步骤一：调用个人核身扫脸api接口/identity_service/wbsb6y；步骤二：请求参数中设置showAgreement=true；步骤三：启动扫脸核身流程；步骤四：检查扫脸页面协议展示；步骤五：完成扫脸认证流程；步骤六：验证协议展示不影响扫脸功能；

##### ER-预期结果：1：扫脸接口调用成功；2：扫脸页面正常加载；3：页面中显示相关协议内容；4：协议展示不干扰扫脸操作；5：扫脸认证功能正常；6：协议优先级高于签署侧校验；

#### TL-个人核身三要素api接口showAgreement参数验证

##### PD-前置条件：三要素验证接口可用；

##### 步骤一：调用个人核身三要素api接口/identity_service/wry7rc；步骤二：设置showAgreement=true；步骤三：输入姓名、身份证号、手机号三要素信息；步骤四：提交三要素验证请求；步骤五：检查验证页面协议展示；步骤六：完成三要素验证流程；

##### ER-预期结果：1：三要素接口调用成功；2：验证页面正常展示；3：页面中显示协议内容；4：三要素验证功能正常；5：协议展示不影响验证流程；

#### TL-个人核身四要素api接口showAgreement参数完整验证

##### PD-前置条件：四要素验证接口可用；银行卡验证可用；

##### 步骤一：调用个人核身四要素api接口/identity_service/tgp6t3；步骤二：设置showAgreement=true；步骤三：输入姓名、身份证号、手机号、银行卡号四要素信息；步骤四：提交四要素验证请求；步骤五：检查验证页面协议展示；步骤六：完成四要素验证流程；步骤七：验证企业证书协议展示逻辑；

##### ER-预期结果：1：四要素接口调用成功；2：验证页面正常展示；3：页面中显示完整协议内容；4：根据认证用途正确展示企业证书协议；5：四要素验证功能正常；6：银行卡验证通过；

### 功能闭环场景验证

#### TL-新用户完整核身流程协议展示闭环验证

##### PD-前置条件：新用户账户；核身接口可用；

##### 步骤一：新用户首次访问核身页面；步骤二：选择核身方式并设置showAgreement=true；步骤三：阅读并同意协议内容；步骤四：完成核身验证流程；步骤五：验证核身结果和协议记录；

##### ER-预期结果：1：新用户可正常访问核身功能；2：协议内容完整展示；3：用户可正常阅读和同意协议；4：核身流程顺利完成；5：协议同意记录正确保存；

#### TL-老用户核身流程协议展示变更闭环验证

##### PD-前置条件：老用户账户；历史核身记录；

##### 步骤一：老用户访问核身页面；步骤二：系统检测用户历史记录；步骤三：根据showAgreement参数控制协议展示；步骤四：完成核身验证流程；步骤五：对比协议展示前后差异；

##### ER-预期结果：1：老用户可正常访问核身功能；2：系统正确识别用户历史状态；3：协议展示符合参数设置；4：核身流程正常完成；5：协议展示变更不影响核身功能；

## 需求2：V3账号解绑链接支持微信小程序redirectUrl

### 功能测试

#### TL-V3账号解绑接口支持wechat://back参数验证

##### PD-前置条件：V3账号接口可用；微信小程序环境；账号已绑定状态；

##### 步骤一：在微信小程序中调用V3账号解绑接口；步骤二：设置redirectUrl参数为"wechat://back"；步骤三：执行账号解绑操作；步骤四：确认解绑操作成功；步骤五：检查页面跳转行为；步骤六：验证微信back方法调用；

##### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：解绑状态更新正确；4：成功唤起微信back方法；5：用户返回到上一页面；6：跳转过程流畅无卡顿；

#### TL-V3账号解绑接口普通redirectUrl参数兼容性验证

##### PD-前置条件：V3账号接口可用；账号已绑定状态；

##### 步骤一：调用V3账号解绑接口；步骤二：设置redirectUrl参数为普通HTTP URL；步骤三：执行账号解绑操作；步骤四：确认解绑操作成功；步骤五：检查页面跳转结果；

##### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：页面正确跳转到指定URL；4：原有跳转功能保持兼容；5：不影响非微信小程序场景；

#### TL-V3账号解绑接口redirectUrl参数为空时默认行为验证

##### PD-前置条件：V3账号接口可用；账号已绑定状态；

##### 步骤一：调用V3账号解绑接口；步骤二：不传redirectUrl参数或传空值；步骤三：执行账号解绑操作；步骤四：确认解绑操作成功；步骤五：检查系统默认跳转行为；

##### ER-预期结果：1：解绑接口调用成功；2：账号解绑操作完成；3：使用系统默认跳转逻辑；4：不影响解绑核心功能；5：向后兼容性良好；

### 功能闭环场景验证

#### TL-微信小程序用户完整解绑流程闭环验证

##### PD-前置条件：微信小程序环境；用户已绑定多个账号；

##### 步骤一：用户在微信小程序中查看绑定账号列表；步骤二：选择需要解绑的账号；步骤三：确认解绑操作并设置redirectUrl为"wechat://back"；步骤四：系统执行解绑流程；步骤五：解绑成功后自动跳转；步骤六：验证账号列表更新和用户体验；

##### ER-预期结果：1：用户可正常查看绑定账号；2：解绑操作流程清晰；3：解绑功能执行成功；4：页面跳转符合微信小程序规范；5：账号列表实时更新；6：整体用户体验良好；

## 需求3：国际短信扣费子产品上游适配

### 功能测试

#### TL-国内手机号发送意愿认证短信扣费验证

##### PD-前置条件：短信接口可用；扣费接口可用；客户账户余额充足；

##### 步骤一：使用国内手机号（如13812345678）发起意愿认证；步骤二：调用/v1/willingness/sys_transId/createCodeAuth接口；步骤三：触发短信验证码发送；步骤四：检查短信发送状态；步骤五：查询扣费记录和金额；步骤六：验证扣费子产品类型；

##### ER-预期结果：1：短信发送成功；2：用户收到验证码短信；3：按国内短信标准扣费；4：扣费记录准确完整；5：扣费金额符合国内短信定价；6：扣费子产品为普通短信产品；

#### TL-国际手机号发送意愿认证短信扣费验证

##### PD-前置条件：短信接口可用；扣费接口可用；客户账户余额充足；国际短信子产品已开通；

##### 步骤一：使用国际手机号（如+1234567890）发起意愿认证；步骤二：调用/v1/willingness/sys_transId/createCodeAuth接口；步骤三：触发短信验证码发送；步骤四：检查短信发送状态；步骤五：查询扣费记录和金额；步骤六：验证扣费子产品code；

##### ER-预期结果：1：短信发送成功；2：用户收到验证码短信；3：按国际短信标准扣费；4：扣费金额为国内短信的十几二十倍；5：扣费子产品code为service-C-127；6：扣费记录标注为国际短信；

#### TL-存量客户白名单控制国际短信扣费验证

##### PD-前置条件：短信接口可用；客户在白名单中；扣费接口可用；

##### 步骤一：确认客户在国际短信白名单中；步骤二：使用国际手机号发送意愿认证短信；步骤三：调用短信发送接口；步骤四：检查扣费处理逻辑；步骤五：验证扣费金额和标准；

##### ER-预期结果：1：短信发送成功；2：按普通短信费用扣费；3：不按国际短信标准收费；4：白名单控制机制生效；5：扣费记录标注白名单处理；

#### TL-非白名单客户国际短信扣费验证

##### PD-前置条件：短信接口可用；客户不在白名单中；扣费接口可用；

##### 步骤一：确认客户不在国际短信白名单中；步骤二：使用国际手机号发送意愿认证短信；步骤三：调用短信发送接口；步骤四：检查扣费处理逻辑；步骤五：验证扣费金额和标准；

##### ER-预期结果：1：短信发送成功；2：按国际短信费用扣费；3：扣费金额较高；4：默认收费逻辑生效；5：扣费子产品code为service-C-127；

### 功能闭环场景验证

#### TL-快捷签客户国际短信使用完整流程闭环验证

##### PD-前置条件：快捷签客户账户；国际短信子产品已配置；

##### 步骤一：快捷签客户发起签署流程；步骤二：输入国际手机号进行意愿认证；步骤三：系统识别手机号类型并计算扣费；步骤四：发送国际短信验证码；步骤五：用户输入验证码完成认证；步骤六：完成签署流程并查看扣费明细；

##### ER-预期结果：1：快捷签流程正常启动；2：系统正确识别国际手机号；3：扣费计算准确；4：国际短信发送成功；5：意愿认证完成；6：签署流程正常结束；7：扣费明细清晰准确；

## 需求4：企业法人意愿认证方式调整

### 功能测试

#### TL-企业法人刷脸加组织机构四要素认证流程验证

##### PD-前置条件：v2版本企业实名接口可用；法人刷脸接口可用；组织机构验证接口可用；

##### 步骤一：访问v2版本企业实名页面/v2/identity/auth/web/organization/{flowId}/infoVerify；步骤二：选择企业法人认证方式；步骤三：进行法人本人刷脸认证；步骤四：刷脸认证通过后进入四要素验证；步骤五：输入组织机构名称、统一社会信用代码、法人姓名、法人身份证号；步骤六：提交四要素验证请求；步骤七：完成整个认证流程；

##### ER-预期结果：1：企业实名页面正常加载；2：法人刷脸认证成功；3：组织机构四要素信息比对通过；4：认证流程顺畅完整；5：认证结果准确可靠；6：认证方式记录为"法人刷脸+组织机构四要素信息比对"；

#### TL-认证方式显示调整验证

##### PD-前置条件：企业已完成新认证方式；运营支撑平台可用；

##### 步骤一：企业完成法人刷脸+四要素认证；步骤二：登录运营支撑平台；步骤三：查看企业实名认证详情；步骤四：检查认证管理子流程类型显示；步骤五：验证认证方式描述准确性；

##### ER-预期结果：1：认证详情页面正常显示；2：子流程类型显示为"法人刷脸+组织机构四要素信息比对"；3：不再显示"组织机构三要素信息比对"；4：认证信息描述准确严谨；5：历史认证记录正确更新；

#### TL-存证出证信息调整验证

##### PD-前置条件：企业已完成新认证方式；存证接口可用；出证接口可用；

##### 步骤一：触发企业认证存证流程；步骤二：生成企业认证证书；步骤三：检查证书中认证方式描述；步骤四：验证证书法律效力相关信息；步骤五：对比调整前后证书内容差异；

##### ER-预期结果：1：存证流程正常执行；2：认证证书成功生成；3：证书中认证方式描述为"法人刷脸+组织机构四要素信息比对"；4：证书具有完整法律效力；5：认证信息更加严谨可信；6：证书内容符合监管要求；

### 功能闭环场景验证

#### TL-企业法人认证完整业务流程验证场景

##### PD-前置条件：企业账户；法人信息完整；相关认证接口可用；

##### 步骤一：企业发起实名认证申请；步骤二：选择法人本人认证方式；步骤三：法人进行刷脸认证；步骤四：系统验证法人身份信息；步骤五：进入组织机构四要素验证环节；步骤六：输入完整的企业四要素信息；步骤七：系统进行四要素信息比对；步骤八：认证通过后更新企业状态；步骤九：生成认证证书和相关凭证；步骤十：通知企业认证完成；

##### ER-预期结果：1：认证申请提交成功；2：法人刷脸认证通过；3：四要素信息比对成功；4：企业认证状态正确更新；5：认证证书内容准确；6：企业收到认证完成通知；7：整个流程用户体验良好；8：认证结果具有法律效力；

## 需求5：SSO登录支持人机校验

### 功能测试

#### TL-SSO登录人机校验接口调用验证

##### PD-前置条件：SSO接口可用；极验接口可用；

##### 步骤一：获取验证码之前访问SSO登录页面；步骤二：调用人机校验接口/account-webserver/sender/robotAuth/apply/v3；步骤三：获取极验参数（challenge、gt等）；步骤四：检查返回参数格式和内容；步骤五：验证参数有效性；

##### ER-预期结果：1：人机校验接口调用成功；2：返回code为0表示成功；3：data中包含success、newCaptcha、challenge、gt参数；4：challenge和gt参数格式正确；5：参数可用于后续极验流程；

#### TL-SSO发送验证码接口极验参数校验

##### PD-前置条件：SSO接口可用；极验接口可用；已获取有效极验参数；

##### 步骤一：完成人机校验获取校验结果；步骤二：调用发送验证码接口/account-webserver/login/sso/account/bindAccount/send；步骤三：在请求中传入credentials和type参数；步骤四：在data.robotModel中传入geetest_challenge、geetest_validate、geetest_seccode；步骤五：提交请求并检查响应；

##### ER-预期结果：1：发送验证码接口调用成功；2：极验参数校验通过；3：验证码发送成功；4：返回authCodeId任务ID；5：用户收到验证码短信；

#### TL-SSO绑定登录接口流程验证

##### PD-前置条件：SSO接口可用；已完成人机校验和验证码发送；

##### 步骤一：用户输入收到的验证码；步骤二：调用绑定三方账号登录接口/login/sso/account/bindAccount/check；步骤三：提交验证码和相关参数；步骤四：完成账号绑定和登录；步骤五：验证登录状态和权限；

##### ER-预期结果：1：绑定登录接口调用成功；2：验证码校验通过；3：账号绑定成功；4：用户登录状态正确；5：获得相应系统权限；

#### TL-极验服务异常时降级处理验证

##### PD-前置条件：SSO接口可用；极验接口异常或不可用；

##### 步骤一：访问SSO登录页面；步骤二：尝试调用人机校验接口；步骤三：检测到极验接口异常；步骤四：系统启动降级模式；步骤五：直接调用发送验证码接口（不传极验参数）；步骤六：完成登录流程；

##### ER-预期结果：1：系统检测到极验接口异常；2：自动启用降级模式；3：不展示极验组件；4：后端不进行极验信息校验；5：登录流程正常进行；6：用户无感知切换；

### 功能闭环场景验证

#### TL-SSO登录人机校验完整业务流程验证场景

##### PD-前置条件：SSO接口可用；极验接口可用；短信接口可用；

##### 步骤一：用户访问需要SSO登录的页面；步骤二：点击SSO登录按钮；步骤三：系统调用人机校验接口获取参数；步骤四：前端展示极验人机校验组件；步骤五：用户完成人机校验操作；步骤六：获取校验结果参数；步骤七：调用发送验证码接口并传入极验参数；步骤八：后端校验极验参数有效性；步骤九：发送短信验证码给用户；步骤十：用户输入验证码；步骤十一：调用绑定登录接口完成登录；步骤十二：用户成功登录系统；

##### ER-预期结果：1：整个流程无缝衔接；2：人机校验有效防止机器操作；3：验证码发送成功；4：登录功能正常；5：用户体验流畅；6：安全性得到提升；7：系统性能稳定；

#### TL-PC端和H5端SSO人机校验兼容性验证场景

##### PD-前置条件：SSO接口可用；极验接口可用；PC和H5环境可用；

##### 步骤一：分别在PC端和H5端访问SSO登录页面；步骤二：在两个环境中进行人机校验；步骤三：完成验证码发送和登录流程；步骤四：对比两个环境的用户体验；步骤五：验证功能一致性；

##### ER-预期结果：1：PC端和H5端功能一致；2：极验组件在两个环境正常展示；3：交互体验适配良好；4：登录成功率一致；5：性能表现稳定；

### 边界测试

#### TL-showAgreement参数类型边界验证

##### PD-前置条件：核身接口可用；

##### 步骤一：调用核身接口传入showAgreement="true"（字符串）；步骤二：调用核身接口传入showAgreement=1（数字）；步骤三：调用核身接口传入showAgreement=null；步骤四：检查各种参数类型的处理结果；

##### ER-预期结果：1：字符串类型返回参数格式错误；2：数字类型返回参数格式错误；3：null值按默认false处理；4：只接受布尔类型参数；

#### TL-redirectUrl参数长度边界验证

##### PD-前置条件：V3账号接口可用；

##### 步骤一：传入超长redirectUrl（>2048字符）；步骤二：传入空字符串redirectUrl；步骤三：传入特殊字符redirectUrl；步骤四：检查系统处理结果；

##### ER-预期结果：1：超长URL返回参数长度错误；2：空字符串使用默认处理；3：特殊字符进行安全过滤；4：参数验证机制有效；

#### TL-手机号格式边界验证

##### PD-前置条件：短信接口可用；

##### 步骤一：使用无效格式手机号发送短信；步骤二：使用边界长度手机号发送短信；步骤三：使用特殊字符手机号发送短信；步骤四：验证手机号格式校验；

##### ER-预期结果：1：无效格式返回手机号格式错误；2：边界长度正确处理；3：特殊字符被拒绝；4：格式校验严格有效；

### 异常测试

#### TL-协议服务异常时核身功能验证

##### PD-前置条件：核身接口可用；协议服务异常；

##### 步骤一：调用核身接口设置showAgreement=true；步骤二：协议服务返回异常；步骤三：检查核身功能是否受影响；

##### ER-预期结果：1：核身功能正常进行；2：协议展示失败不影响核身；3：记录协议服务异常日志；4：用户收到友好提示；

#### TL-扣费系统异常时短信发送验证

##### PD-前置条件：短信接口可用；扣费系统异常；

##### 步骤一：使用国际手机号发送短信；步骤二：扣费系统无法正常扣费；步骤三：检查短信发送处理；

##### ER-预期结果：1：短信发送被阻止；2：返回扣费异常错误；3：不发送短信避免损失；4：记录异常日志；

#### TL-极验服务间歇性异常处理验证

##### PD-前置条件：SSO接口可用；极验服务间歇性异常；

##### 步骤一：用户开始SSO登录流程；步骤二：人机校验过程中极验服务异常；步骤三：系统检测异常并处理；

##### ER-预期结果：1：系统检测到服务异常；2：自动切换到降级模式；3：用户可继续登录流程；4：异常处理透明化；

### 性能测试

#### TL-核身接口协议展示性能验证

##### PD-前置条件：测试环境稳定；核身接口可用；

##### 步骤一：并发调用核身接口（showAgreement=true）；步骤二：并发调用核身接口（showAgreement=false）；步骤三：对比两种情况的响应时间；

##### ER-预期结果：1：协议展示不显著影响性能；2：响应时间差异小于500ms；3：并发处理能力正常；

#### TL-国际短信发送性能验证

##### PD-前置条件：测试环境稳定；短信接口可用；

##### 步骤一：批量发送国际短信；步骤二：批量发送国内短信；步骤三：对比发送性能差异；

##### ER-预期结果：1：国际短信发送成功率>95%；2：平均发送时间<10秒；3：扣费计算准确及时；

#### TL-SSO人机校验性能验证

##### PD-前置条件：测试环境稳定；相关接口可用；

##### 步骤一：并发进行人机校验；步骤二：测试极验组件加载时间；步骤三：测试校验响应时间；

##### ER-预期结果：1：组件加载时间<2秒；2：校验响应时间<3秒；3：并发处理能力满足需求；

### 安全测试

#### TL-核身接口SQL注入安全验证

##### PD-前置条件：核身接口可用；

##### 步骤一：在showAgreement参数中注入SQL语句；步骤二：在其他参数中注入恶意代码；步骤三：提交请求检查系统响应；

##### ER-预期结果：1：系统拒绝恶意参数；2：返回参数格式错误；3：不执行注入代码；4：记录安全事件；

#### TL-极验参数篡改安全验证

##### PD-前置条件：SSO接口可用；极验接口可用；

##### 步骤一：获取正常极验参数；步骤二：篡改关键校验参数；步骤三：尝试绕过人机校验；

##### ER-预期结果：1：系统检测到参数篡改；2：校验失败拒绝请求；3：记录安全事件日志；4：防止恶意绕过；

#### TL-国际短信费用攻击防护验证

##### PD-前置条件：短信接口可用；扣费接口可用；

##### 步骤一：短时间内大量发送国际短信；步骤二：尝试绕过扣费机制；步骤三：检查系统防护措施；

##### ER-预期结果：1：系统检测到异常行为；2：触发频率限制机制；3：阻止恶意消费；4：保护客户资金安全；

### 兼容性测试

#### TL-多端核身接口协议展示兼容性验证

##### PD-前置条件：核身接口可用；PC、H5、客户端、微信小程序、支付宝小程序环境可用；

##### 步骤一：在各端调用核身接口设置showAgreement=true；步骤二：检查协议展示效果；步骤三：验证功能一致性；

##### ER-预期结果：1：各端协议展示一致；2：交互体验适配良好；3：功能完整可用；4：性能表现稳定；

#### TL-微信小程序解绑功能多设备兼容性验证

##### PD-前置条件：V3账号接口可用；主流设备型号可用；

##### 步骤一：在不同设备的微信小程序中执行解绑；步骤二：验证wechat://back跳转效果；步骤三：检查兼容性问题；

##### ER-预期结果：1：各设备解绑功能正常；2：跳转效果一致；3：无兼容性问题；4：用户体验良好；
